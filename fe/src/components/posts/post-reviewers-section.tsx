import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible'
import { Badge } from '@/components/ui/badge'
import { ChevronDown, ChevronRight, Users, Clock, CheckCircle, XCircle } from 'lucide-react'
import { cn } from '@/lib/utils'
import { useTranslations } from '@/lib/i18n/typed-translations'
import { ReviewerCard } from './reviewer-card'
import type { PostReviewer as BasePostReviewer } from '@/lib/types/api'

// Extended PostReviewer type with enhanced review information
interface PostReviewer extends BasePostReviewer {
  review_notes?: string
  assigned_at?: string
  reviewed_at?: string
  checklist?: any[]
  is_pending?: boolean
}

interface PostReviewersSectionProps {
  reviewers: PostReviewer[]
  compact?: boolean
  showTimeline?: boolean
  className?: string
}

export function PostReviewersSection({ 
  reviewers, 
  compact = false, 
  showTimeline = false,
  className 
}: PostReviewersSectionProps) {
  const [isExpanded, setIsExpanded] = useState(!compact)
  const { t, keys } = useTranslations()

  if (!reviewers || reviewers.length === 0) {
    return (
      <div className={cn("text-sm text-muted-foreground", className)}>
        No reviewers assigned
      </div>
    )
  }

  // Calculate review statistics
  const totalReviewers = reviewers.length
  const approvedCount = reviewers.filter(r => r.status === 'approved').length
  const reworkCount = reviewers.filter(r => r.status === 'rework').length
  const pendingCount = reviewers.filter(r => r.status === 'pending').length

  const getOverallStatus = () => {
    if (reworkCount > 0) return 'rework'
    if (pendingCount > 0) return 'pending'
    if (approvedCount === totalReviewers) return 'approved'
    return 'mixed'
  }

  const getOverallStatusColor = (status: string) => {
    switch (status) {
      case 'approved': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
      case 'rework': return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
      case 'pending': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
      default: return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'approved': return <CheckCircle className="h-3 w-3" />
      case 'rework': return <XCircle className="h-3 w-3" />
      case 'pending': return <Clock className="h-3 w-3" />
      default: return <Users className="h-3 w-3" />
    }
  }

  const overallStatus = getOverallStatus()

  // Compact view for post cards
  if (compact) {
    return (
      <div className={cn("space-y-2", className)}>
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2 text-sm text-muted-foreground">
            <Users className="h-4 w-4" />
            <span>{t(keys.collaborationHubs.posts.reviewers)}</span>
          </div>
          <Badge variant="outline" className={getOverallStatusColor(overallStatus)}>
            {getStatusIcon(overallStatus)}
            <span className="ml-1">
              {approvedCount}/{totalReviewers} approved
            </span>
          </Badge>
        </div>

        {/* Quick status breakdown */}
        <div className="flex items-center gap-2 text-xs text-muted-foreground">
          {approvedCount > 0 && (
            <span className="flex items-center gap-1">
              <CheckCircle className="h-3 w-3 text-green-500" />
              {approvedCount} approved
            </span>
          )}
          {pendingCount > 0 && (
            <span className="flex items-center gap-1">
              <Clock className="h-3 w-3 text-yellow-500" />
              {pendingCount} pending
            </span>
          )}
          {reworkCount > 0 && (
            <span className="flex items-center gap-1">
              <XCircle className="h-3 w-3 text-red-500" />
              {reworkCount} rework
            </span>
          )}
        </div>

        {/* Expandable reviewer list */}
        <Collapsible open={isExpanded} onOpenChange={setIsExpanded}>
          <CollapsibleTrigger asChild>
            <Button variant="ghost" size="sm" className="w-full justify-between p-2 h-auto">
              <span className="text-sm">
                {isExpanded ? 'Hide reviewers' : 'Show reviewers'}
              </span>
              {isExpanded ? (
                <ChevronDown className="h-4 w-4" />
              ) : (
                <ChevronRight className="h-4 w-4" />
              )}
            </Button>
          </CollapsibleTrigger>
          
          <CollapsibleContent className="space-y-2 pt-2">
            {reviewers.map((reviewer) => (
              <ReviewerCard
                key={reviewer.id}
                reviewer={reviewer}
                compact={true}
                showDetails={false}
              />
            ))}
          </CollapsibleContent>
        </Collapsible>
      </div>
    )
  }

  // Full view for post dialogs
  return (
    <div className={cn("space-y-4", className)}>
      <div className="flex items-center justify-between">
        <h4 className="font-medium text-sm flex items-center gap-2">
          <Users className="h-4 w-4" />
          Assigned Reviewers ({totalReviewers})
        </h4>
        <Badge variant="outline" className={getOverallStatusColor(overallStatus)}>
          {getStatusIcon(overallStatus)}
          <span className="ml-1">
            {approvedCount}/{totalReviewers} approved
          </span>
        </Badge>
      </div>

      {/* Status summary */}
      <div className="flex items-center gap-4 text-sm">
        {approvedCount > 0 && (
          <div className="flex items-center gap-1 text-green-600 dark:text-green-400">
            <CheckCircle className="h-4 w-4" />
            <span>{approvedCount} approved</span>
          </div>
        )}
        {pendingCount > 0 && (
          <div className="flex items-center gap-1 text-yellow-600 dark:text-yellow-400">
            <Clock className="h-4 w-4" />
            <span>{pendingCount} pending</span>
          </div>
        )}
        {reworkCount > 0 && (
          <div className="flex items-center gap-1 text-red-600 dark:text-red-400">
            <XCircle className="h-4 w-4" />
            <span>{reworkCount} need rework</span>
          </div>
        )}
      </div>

      {/* Individual reviewer cards */}
      <div className="space-y-3">
        {showTimeline ? (
          // Sort by status for timeline view (for now, until API types are updated)
          [...reviewers]
            .sort((a, b) => {
              // Sort by status: rework, pending, approved
              const statusOrder = { rework: 0, pending: 1, approved: 2 }
              const aOrder = statusOrder[a.status as keyof typeof statusOrder] ?? 3
              const bOrder = statusOrder[b.status as keyof typeof statusOrder] ?? 3
              return aOrder - bOrder
            })
            .map((reviewer) => (
              <ReviewerCard
                key={reviewer.id}
                reviewer={reviewer}
                showDetails={true}
              />
            ))
        ) : (
          // Default order: status priority (rework, pending, approved)
          [...reviewers]
            .sort((a, b) => {
              const statusOrder = { rework: 0, pending: 1, approved: 2 }
              const aOrder = statusOrder[a.status as keyof typeof statusOrder] ?? 3
              const bOrder = statusOrder[b.status as keyof typeof statusOrder] ?? 3
              return aOrder - bOrder
            })
            .map((reviewer) => (
              <ReviewerCard
                key={reviewer.id}
                reviewer={reviewer}
                showDetails={true}
              />
            ))
        )}
      </div>
    </div>
  )
}
